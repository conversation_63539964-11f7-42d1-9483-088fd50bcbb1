# S3 object for Lambda deployment package
resource "aws_s3_object" "acd_processor_lambda_code" {
  bucket = var.s3_lambda_code_bucket_name
  key    = "smart-analytics-acd-processor.zip"
  source = "${var.project_dir}/smart-analytics-acd-processor.zip"
  server_side_encryption = "aws:kms"
  kms_key_id             = "arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:key/${var.kms_key_name}"

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor-lambda-code"
    }
  )
}

# CloudWatch log group for Lambda function logs with 1-year retention
resource "aws_cloudwatch_log_group" "acd_processor_log_group" {
  name              = "/aws/lambda/${var.name_prefix}-acd-processor"
  retention_in_days = 365

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor-log-group"
    }
  )
}

# Lambda function for processing ACD data from SQS queues
resource "aws_lambda_function" "acd_processor" {
  function_name                  = "${var.name_prefix}-acd-processor"
  role                           = aws_iam_role.acd_processor_lambda_role.arn
  s3_bucket                      = aws_s3_object.acd_processor_lambda_code.bucket
  s3_key                         = aws_s3_object.acd_processor_lambda_code.key
  source_code_hash               = aws_s3_object.acd_processor_lambda_code.checksum_sha256
  runtime                        = "python3.12"
  handler                        = "lambda_function.lambda_handler"
  memory_size                    = var.lambda_memory_size
  timeout                        = var.lambda_timeout
  reserved_concurrent_executions = var.lambda_reserved_concurrency

  environment {
    variables = var.environment_variables
  }

  vpc_config {
    subnet_ids         = var.subnet_ids
    security_group_ids = var.security_group_ids
  }

  depends_on = [
    aws_iam_role_policy_attachment.acd_processor_lambda_vpc_execution,
    aws_cloudwatch_log_group.acd_processor_log_group,
  ]

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor"
    }
  )
}

# Event source mapping to trigger Lambda from main SQS queue
resource "aws_lambda_event_source_mapping" "acd_processor_sqs_trigger" {

  event_source_arn                   = "arn:${data.aws_partition.current.partition}:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.lambda_trigger_queue_name}"
  function_name                      = aws_lambda_function.acd_processor.function_name
  batch_size                         = var.sqs_batch_size
  maximum_batching_window_in_seconds = var.sqs_max_batching_window_seconds
  # Scaling configuration to control concurrent executions
  scaling_config {
    maximum_concurrency = var.lambda_max_concurrency
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor-sqs-trigger"
    }
  )
}

# Event source mapping to trigger Lambda from dead letter queue for error processing
resource "aws_lambda_event_source_mapping" "acd_processor_dlq_trigger" {
  event_source_arn                   = "arn:${data.aws_partition.current.partition}:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.lambda_trigger_dlq_name}"
  function_name                      = aws_lambda_function.acd_processor.function_name
  batch_size                         = var.sqs_batch_size
  maximum_batching_window_in_seconds = var.sqs_max_batching_window_seconds

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor-dlq-trigger"
    }
  )
}





