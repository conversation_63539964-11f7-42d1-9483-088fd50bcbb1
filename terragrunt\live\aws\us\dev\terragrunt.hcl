# Development environment configuration for US region
# This file configures the smart analytics processors for the dev environment

# Environment-specific inputs
inputs = {
  # AWS Configuration
  region      = "us-east-1"
  environment = "dev"
  country     = "us"

  # Global tags applied to all resources
  global_tags = {
    Environment = "dev"
    Project     = "smartanalytics"
    Owner       = "csa"
    ManagedBy   = "terragrunt"
    Country     = "us"
  }

  # SNS topic for critical alerts
  slack_errors_alarm_topic_name = "csa-critical-alarms"

  # Project directory (set by CI/CD or local development)
  project_dir = "."

  # Customer configuration for development environment
  service_customer_config = {
    # Example customer configuration
    customer1 = {
      # S3 bucket for Lambda code (must exist)
      s3_lambda_code_bucket_name = "smartanalytics-lambda-code-dev-us"
      
      # KMS key for encryption
      kms_key_name = "alias/smartanalytics-dev-us"
      
      # ACD processor configuration
      acd = {
        # Enable ACD processing for this customer
        enable = true
        
        # SQS queue names (must exist)
        queue_name = "smartanalytics-customer1-acd-queue-dev"
        dlq_name   = "smartanalytics-customer1-acd-dlq-dev"
        
        # VPC configuration (update with actual values)
        security_group_ids = ["sg-0123456789abcdef0"]  # Update with actual security group
        subnet_ids         = ["subnet-0123456789abcdef0", "subnet-0fedcba9876543210"]  # Update with actual subnets
        vpc_id             = "vpc-0123456789abcdef0"  # Update with actual VPC ID
        
        # Redshift configuration (update with actual values)
        redshift_secret_name         = "smartanalytics/redshift/customer1/dev"
        redshift_cluster_identifier  = "smartanalytics-customer1-dev"
        
        # Lambda environment variables
        environment_variables = {
          REDSHIFT_DATABASE = "customer1_dev"
          LOG_LEVEL        = "DEBUG"  # More verbose logging for dev
          BATCH_SIZE       = "10"     # Smaller batches for testing
          ENVIRONMENT      = "dev"
        }
      }
      
      # Customer-specific tags
      tags = {
        Customer   = "customer1"
        CostCenter = "analytics"
        Tier       = "development"
      }
    }

    # Add more customers as needed for development testing
    # customer2 = {
    #   s3_lambda_code_bucket_name = "smartanalytics-lambda-code-dev-us"
    #   kms_key_name = "alias/smartanalytics-dev-us"
    #   acd = {
    #     enable = false  # Disable for this customer in dev
    #     ...
    #   }
    #   tags = {
    #     Customer = "customer2"
    #   }
    # }
  }
}


# Include the root terragrunt.hcl configuration
include "root" {
  expose = true
  path = find_in_parent_folders()
}

# Specify the Terraform source code location
terraform {
  source = "../../../../../terraform//"
}