# ACD Processor Module

This Terraform module deploys an AWS Lambda function for processing ACD (Automatic Call Distribution) data from SQS queues and storing it in Amazon Redshift.

## Architecture

The module creates:
- Lambda function with VPC configuration for secure Redshift access
- CloudWatch log group with 1-year retention
- IAM role with least-privilege permissions
- Event source mappings for SQS queue and DLQ processing
- CloudWatch alarms for monitoring function health

## Features

- **Secure**: KMS-encrypted S3 objects, VPC deployment, least-privilege IAM
- **Scalable**: Configurable concurrency limits and batch processing
- **Monitored**: Comprehensive CloudWatch alarms for errors, duration, and invocation anomalies
- **Resilient**: Dead letter queue processing for error handling

## Usage

```hcl
module "acd_processor" {
  source = "./modules/acd-processor"
  
  name_prefix                   = "dev-us-customer1-smartanalytics"
  kms_key_name                 = "alias/lambda-code-key"
  s3_lambda_code_bucket_name   = "lambda-code-bucket"
  slack_errors_alarm_topic_name = "slack-alerts"
  
  lambda_trigger_queue_name = "acd-processing-queue"
  lambda_trigger_dlq_name   = "acd-processing-dlq"
  security_group_ids        = ["sg-12345678"]
  subnet_ids               = ["subnet-12345678", "subnet-87654321"]
  vpc_id                   = "vpc-12345678"
  
  redshift_secret_name         = "redshift/credentials"
  redshift_cluster_identifier  = "redshift-cluster"
  environment_variables        = {
    REDSHIFT_DATABASE = "analytics"
    LOG_LEVEL        = "INFO"
  }
  
  # Performance tuning
  lambda_memory_size           = 512
  lambda_timeout              = 300
  lambda_reserved_concurrency = 10
  lambda_max_concurrency      = 5
  
  # SQS configuration
  sqs_batch_size                    = 10
  sqs_max_batching_window_seconds   = 5
  
  tags = {
    Environment = "dev"
    Project     = "smartanalytics"
    Owner       = "Smart Analytics Team"
  }
  
  project_dir = "/path/to/project"
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.6 |
| aws | >= 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 5.0 |

## Resources

| Name | Type |
|------|------|
| aws_s3_object.acd_processor_lambda_code | resource |
| aws_cloudwatch_log_group.acd_processor_log_group | resource |
| aws_lambda_function.acd_processor | resource |
| aws_lambda_event_source_mapping.acd_processor_sqs_trigger | resource |
| aws_lambda_event_source_mapping.acd_processor_dlq_trigger | resource |
| aws_iam_role.acd_processor_lambda_role | resource |
| aws_iam_role_policy.acd_processor_lambda_policy | resource |
| aws_iam_role_policy_attachment.acd_processor_lambda_vpc_execution | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name_prefix | String to use as name_prefix for resource names | `string` | n/a | yes |
| security_group_ids | List of security group IDs to associate with the Lambda function | `list(string)` | n/a | yes |
| subnet_ids | List of subnet IDs to associate with the Lambda function | `list(string)` | n/a | yes |
| vpc_id | VPC ID where the Lambda function will be deployed | `string` | n/a | yes |
| slack_errors_alarm_topic_name | Name of the SNS topic for data lake CloudWatch alarms | `string` | n/a | yes |
| redshift_secret_name | The name of the Redshift secret in AWS Secrets Manager | `string` | n/a | yes |
| redshift_cluster_identifier | The identifier of the Redshift cluster | `string` | n/a | yes |
| project_dir | Full path to working directory passed from GitLab CI | `string` | n/a | yes |
| s3_lambda_code_bucket_name | The name of the S3 bucket containing the Lambda code | `string` | n/a | yes |
| kms_key_name | The name of the KMS key to use for encryption | `string` | n/a | yes |
| lambda_trigger_queue_name | Lambda trigger to use for deployment | `string` | n/a | yes |
| lambda_trigger_dlq_name | Lambda trigger to use for deployment | `string` | n/a | yes |
| lambda_memory_size | The amount of memory to allocate to the Lambda function | `number` | `256` | no |
| lambda_timeout | The maximum amount of time the Lambda function can run before it is automatically terminated | `number` | `60` | no |
| lambda_reserved_concurrency | The amount of reserved concurrent executions for this Lambda function | `number` | `-1` | no |
| lambda_max_concurrency | Maximum number of concurrent executions for the event source mapping | `number` | `10` | no |
| environment_variables | Environment variables for the Lambda function | `map(string)` | `{}` | no |
| sqs_batch_size | The maximum number of records to retrieve from SQS in a single batch | `number` | `10` | no |
| sqs_max_batching_window_seconds | The maximum amount of time to gather records before invoking the function | `number` | `0` | no |
| tags | List of key/value pairs for tags | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| lambda_function_name | Name of the Lambda function |
| lambda_function_arn | ARN of the Lambda function |
| lambda_function_invoke_arn | Invoke ARN of the Lambda function |
| lambda_role_arn | ARN of the Lambda execution role |
| lambda_role_name | Name of the Lambda execution role |
| cloudwatch_log_group_name | Name of the CloudWatch log group |
| cloudwatch_log_group_arn | ARN of the CloudWatch log group |
| sqs_event_source_mapping_uuid | UUID of the SQS event source mapping |
| dlq_event_source_mapping_uuid | UUID of the DLQ event source mapping |

## Security Considerations

- Lambda function runs in VPC for secure Redshift access
- IAM permissions follow least-privilege principle
- S3 objects are KMS encrypted
- CloudWatch logs are retained for compliance
- Secrets Manager integration for database credentials

## Monitoring

The module includes CloudWatch alarms for:
- Lambda function errors
- Duration warnings (>10 seconds)
- Invocation anomaly detection

All alarms send notifications to the configured SNS topic.
